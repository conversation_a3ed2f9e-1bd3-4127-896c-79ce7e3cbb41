<?php
// Set page variables
$page_title = 'Dashboard';
$additional_css = ['dashboard.css'];
$additional_js = ['dashboard.js'];

// Include header template
require_once '../templates/user/header.php';

// Include database connection
require_once '../config/config.php';
requireLogin();

// Get comprehensive user data and dashboard statistics
try {
    $db = getDB();
    $user_id = $_SESSION['user_id'];
    
    // Get user account information
    $user_sql = "SELECT * FROM accounts WHERE id = ?";
    $user_result = $db->query($user_sql, [$user_id]);
    $user = $user_result->fetch_assoc();
    
    // Get current balance
    $current_balance = $user['balance'] ?? 0;
    
    // Get monthly statistics for transfers
    $current_month = date('Y-m');
    $transfer_stats_sql = "SELECT 
        SUM(CASE WHEN sender_id != ? AND status = 'completed' THEN amount ELSE 0 END) as money_in,
        SUM(CASE WHEN sender_id = ? AND status = 'completed' THEN amount ELSE 0 END) as money_out,
        COUNT(CASE WHEN status = 'completed' AND DATE_FORMAT(created_at, '%Y-%m') = ? THEN 1 END) as transfer_count
        FROM transfers 
        WHERE (sender_id = ? OR recipient_id = ?) 
        AND DATE_FORMAT(created_at, '%Y-%m') = ?";
    
    $transfer_result = $db->query($transfer_stats_sql, [$user_id, $user_id, $current_month, $user_id, $user_id, $current_month]);
    $transfer_stats = $transfer_result->fetch_assoc();

    $money_in = $transfer_stats['money_in'] ?? 0;
    $money_out = $transfer_stats['money_out'] ?? 0;
    $transfer_count = $transfer_stats['transfer_count'] ?? 0;
    
    // Get transaction statistics (credits and debits)
    $transaction_stats_sql = "SELECT 
        SUM(CASE WHEN transaction_type = 'credit' THEN amount ELSE 0 END) as total_credits,
        SUM(CASE WHEN transaction_type = 'debit' THEN amount ELSE 0 END) as total_debits,
        COUNT(CASE WHEN DATE_FORMAT(created_at, '%Y-%m') = ? THEN 1 END) as transaction_count
        FROM transactions 
        WHERE user_id = ? AND status = 'completed'
        AND DATE_FORMAT(created_at, '%Y-%m') = ?";
    
    $transaction_result = $db->query($transaction_stats_sql, [$current_month, $user_id, $current_month]);
    $transaction_stats = $transaction_result->fetch_assoc();
    
    $total_credits = $transaction_stats['total_credits'] ?? 0;
    $total_debits = $transaction_stats['total_debits'] ?? 0;
    $transaction_count = $transaction_stats['transaction_count'] ?? 0;
    
    // Get recent transactions (combined transfers and account transactions)
    $recent_transactions_sql = "
        (SELECT 'transfer' as type, t.id, t.amount, t.description, t.created_at,
                CASE WHEN t.sender_id = ? THEN 'debit' ELSE 'credit' END as transaction_type,
                CASE WHEN t.sender_id = ? THEN t.recipient_name 
                     ELSE (SELECT CONCAT(first_name, ' ', last_name) FROM accounts WHERE id = t.sender_id) 
                END as other_party,
                t.status
         FROM transfers t 
         WHERE (t.sender_id = ? OR t.recipient_id = ?) AND t.status = 'completed')
        UNION ALL
        (SELECT 'transaction' as type, tr.id, tr.amount, tr.description, tr.created_at,
                tr.transaction_type, tr.category as other_party, tr.status
         FROM transactions tr 
         WHERE tr.user_id = ? AND tr.status = 'completed')
        ORDER BY created_at DESC 
        LIMIT 8";
    
    $recent_result = $db->query($recent_transactions_sql, [$user_id, $user_id, $user_id, $user_id, $user_id]);
    $recent_transactions = [];
    while ($row = $recent_result->fetch_assoc()) {
        $recent_transactions[] = $row;
    }
    
    // Get virtual cards information
    $cards_sql = "SELECT COUNT(*) as total_cards, 
                         SUM(current_balance) as total_card_balance,
                         SUM(spending_limit) as total_limit
                  FROM virtual_cards WHERE user_id = ? AND status = 'active'";
    $cards_result = $db->query($cards_sql, [$user_id]);
    $card_stats = $cards_result->fetch_assoc();
    
    $card_count = $card_stats['total_cards'] ?? 0;
    $total_card_balance = $card_stats['total_card_balance'] ?? 0;
    $total_card_limit = $card_stats['total_limit'] ?? 0;
    
    // Get user's primary virtual card for display
    $primary_card_sql = "SELECT * FROM virtual_cards WHERE user_id = ? AND status = 'active' ORDER BY created_at ASC LIMIT 1";
    $primary_card_result = $db->query($primary_card_sql, [$user_id]);
    $primary_card = $primary_card_result->fetch_assoc();
    
    // Get beneficiaries count
    $beneficiaries_sql = "SELECT COUNT(*) as count FROM beneficiaries WHERE user_id = ?";
    $beneficiaries_result = $db->query($beneficiaries_sql, [$user_id]);
    $beneficiaries_count = $beneficiaries_result->fetch_assoc()['count'] ?? 0;

} catch (Exception $e) {
    error_log("Dashboard error: " . $e->getMessage());
    $user = [];
    $current_balance = 0;
    $money_in = $money_out = $transfer_count = $transaction_count = 0;
    $total_credits = $total_debits = $card_count = $total_card_balance = $total_card_limit = 0;
    $beneficiaries_count = 0;
    $recent_transactions = [];
    $primary_card = null;
}

// Calculate percentage changes (mock data for demo)
$money_in_change = 12.5;
$money_out_change = -8.3;
?>

<!-- Include Sidebar -->
<?php require_once '../templates/user/sidebar.php'; ?>

<!-- Main Content -->
<div class="main-content">
    <!-- Welcome Header -->
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="h3 mb-1">Welcome back, <?php echo htmlspecialchars($user['first_name'] ?? 'User'); ?>!</h1>
            <p class="text-muted">Here's your banking overview.</p>
        </div>
    </div>

    <!-- Account Status and Stats Row -->
    <div class="row mb-4">
        <!-- Account Status -->
        <div class="col-md-3">
            <div class="card border-0" style="background: rgba(40, 167, 69, 0.1); border-radius: 12px;">
                <div class="card-body text-center py-4">
                    <div class="d-flex align-items-center justify-content-center mb-2">
                        <i class="fas fa-check-circle text-success me-2" style="font-size: 1.2rem;"></i>
                        <span class="text-success fw-semibold">Active</span>
                    </div>
                    <small class="text-muted"><?php echo htmlspecialchars($user['account_type'] ?? 'Savings'); ?> Account</small>
                </div>
            </div>
        </div>

        <!-- Money In This Month -->
        <div class="col-md-3">
            <div class="card border-0" style="background: rgba(13, 110, 253, 0.1); border-radius: 12px;">
                <div class="card-body text-center py-4">
                    <div class="d-flex align-items-center justify-content-center mb-1">
                        <i class="fas fa-arrow-up text-primary me-2"></i>
                        <span class="text-primary fw-semibold">USD <?php echo number_format($money_in, 2); ?></span>
                    </div>
                    <small class="text-muted">Money In This Month</small>
                    <div class="mt-1">
                        <small class="text-success">+<?php echo $money_in_change; ?>% vs last month</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Money Out This Month -->
        <div class="col-md-3">
            <div class="card border-0" style="background: rgba(220, 53, 69, 0.1); border-radius: 12px;">
                <div class="card-body text-center py-4">
                    <div class="d-flex align-items-center justify-content-center mb-1">
                        <i class="fas fa-arrow-down text-danger me-2"></i>
                        <span class="text-danger fw-semibold">USD <?php echo number_format($money_out, 2); ?></span>
                    </div>
                    <small class="text-muted">Money Out This Month</small>
                    <div class="mt-1">
                        <small class="text-danger"><?php echo $money_out_change; ?>% vs last month</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Current Balance -->
        <div class="col-md-3">
            <div class="card border-0" style="background: rgba(111, 66, 193, 0.1); border-radius: 12px;">
                <div class="card-body text-center py-4">
                    <div class="d-flex align-items-center justify-content-center mb-1">
                        <i class="fas fa-dollar-sign text-purple me-2" style="color: #6f42c1;"></i>
                        <span class="fw-semibold" style="color: #6f42c1;">USD <?php echo number_format($current_balance, 2); ?></span>
                    </div>
                    <small class="text-muted">Current Balance</small>
                    <div class="mt-1">
                        <small class="text-muted">Savings: <?php echo $user['account_type'] ?? 'N/A'; ?></small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content Row -->
    <div class="row">
        <!-- Account Overview -->
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm" style="border-radius: 12px;">
                <div class="card-body p-4">
                    <h5 class="card-title mb-4">Account Overview</h5>
                    
                    <!-- User Info Section -->
                    <div class="d-flex align-items-center mb-4 p-3" style="background: rgba(248, 249, 250, 0.8); border-radius: 10px;">
                        <div class="rounded-circle d-flex align-items-center justify-content-center me-3" 
                             style="width: 50px; height: 50px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; font-weight: bold; font-size: 1.2rem;">
                            <?php echo strtoupper(substr($user['first_name'] ?? 'U', 0, 1) . substr($user['last_name'] ?? 'U', 0, 1)); ?>
                        </div>
                        <div class="flex-grow-1">
                            <h6 class="mb-1"><?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?></h6>
                            <small class="text-muted">Account #<?php echo htmlspecialchars($user['account_number'] ?? 'N/A'); ?></small>
                        </div>
                        <div class="text-end">
                            <span class="badge bg-success px-3 py-2" style="border-radius: 20px;">Active</span>
                        </div>
                    </div>

                    <!-- Balance and Activities -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="text-center p-3" style="background: rgba(13, 110, 253, 0.05); border-radius: 10px;">
                                <h3 class="mb-1 text-primary">USD <?php echo number_format($current_balance, 2); ?></h3>
                                <small class="text-muted">Available Balance</small>
                                <div class="mt-2">
                                    <small class="text-muted">Savings • USD</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="text-center p-3" style="background: rgba(40, 167, 69, 0.05); border-radius: 10px;">
                                <h3 class="mb-1 text-success"><?php echo $transfer_count + $transaction_count; ?></h3>
                                <small class="text-muted">Total Activities This Month</small>
                                <div class="mt-2">
                                    <small class="text-muted"><?php echo $transaction_count > 0 ? 'Recent activity' : 'No transactions yet'; ?></small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Account Status Progress -->
                    <div class="mt-4">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <small class="text-muted">Account Status</small>
                            <small class="text-success fw-semibold">100%</small>
                        </div>
                        <div class="progress" style="height: 6px; border-radius: 10px;">
                            <div class="progress-bar bg-success" style="width: 100%; border-radius: 10px;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Virtual Card Display -->
        <div class="col-lg-4">
            <?php if ($primary_card): ?>
                <!-- Virtual Card -->
                <div class="card border-0 shadow-lg mb-4" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 20px; height: 240px; position: relative; overflow: hidden;">
                    <!-- Card Background Pattern -->
                    <div style="position: absolute; top: -50px; right: -50px; width: 150px; height: 150px; background: rgba(255,255,255,0.1); border-radius: 50%;"></div>
                    <div style="position: absolute; bottom: -30px; left: -30px; width: 100px; height: 100px; background: rgba(255,255,255,0.05); border-radius: 50%;"></div>
                    
                    <div class="card-body d-flex flex-column justify-content-between" style="height: 100%; z-index: 2; position: relative;">
                        <!-- Card Header -->
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <h6 class="mb-1" style="color: rgba(255,255,255,0.9);">PremierBank Pro</h6>
                                <small style="color: rgba(255,255,255,0.7);">Virtual Card</small>
                            </div>
                            <div class="d-flex align-items-center">
                                <i class="fas fa-wifi me-2" style="color: rgba(255,255,255,0.7);"></i>
                                <i class="fab fa-cc-<?php echo $primary_card['card_type']; ?>" style="font-size: 1.5rem; color: rgba(255,255,255,0.9);"></i>
                            </div>
                        </div>
                        
                        <!-- Card Number -->
                        <div class="text-center">
                            <h5 class="mb-0" style="letter-spacing: 3px; font-family: 'Courier New', monospace;">
                                •••• •••• •••• <?php echo substr($primary_card['card_number'], -4); ?>
                            </h5>
                        </div>
                        
                        <!-- Card Footer -->
                        <div class="d-flex justify-content-between align-items-end">
                            <div>
                                <small style="color: rgba(255,255,255,0.7);">Card Holder</small>
                                <div class="fw-bold"><?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?></div>
                            </div>
                            <div class="text-end">
                                <small style="color: rgba(255,255,255,0.7);">Expires</small>
                                <div class="fw-bold"><?php echo sprintf('%02d/%02d', $primary_card['expiry_month'], $primary_card['expiry_year'] % 100); ?></div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Card Actions -->
                <div class="d-flex gap-2 mb-4">
                    <button class="btn btn-outline-primary btn-sm flex-fill">
                        <i class="fas fa-eye me-1"></i>Show Details
                    </button>
                    <button class="btn btn-outline-success btn-sm flex-fill">
                        <i class="fas fa-plus me-1"></i>Top Up
                    </button>
                </div>
            <?php else: ?>
                <!-- No Card Available -->
                <div class="card border-0 shadow-sm mb-4" style="border-radius: 12px;">
                    <div class="card-body text-center py-5">
                        <i class="fas fa-credit-card text-muted mb-3" style="font-size: 3rem;"></i>
                        <h6 class="text-muted">No Virtual Card</h6>
                        <p class="text-muted small">Request a virtual card for secure online payments</p>
                        <a href="<?php echo $base_url; ?>/dashboard/cards/" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus me-1"></i>Request Card
                        </a>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Transaction History Section -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm" style="border-radius: 12px;">
                <div class="card-header bg-white border-0 pt-4 px-4">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">Recent Transactions</h5>
                        <a href="<?php echo $base_url; ?>/dashboard/transactions/" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-list me-1"></i>View All
                        </a>
                    </div>
                </div>
                <div class="card-body px-4 pb-4">
                    <?php if (empty($recent_transactions)): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-receipt text-muted mb-3" style="font-size: 3rem;"></i>
                            <h6 class="text-muted">No Recent Transactions</h6>
                            <p class="text-muted small">Your transaction history will appear here</p>
                            <a href="<?php echo $base_url; ?>/dashboard/transfers/" class="btn btn-primary btn-sm">
                                <i class="fas fa-paper-plane me-1"></i>Make Transfer
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="row g-3">
                            <?php foreach ($recent_transactions as $transaction): ?>
                                <div class="col-md-6">
                                    <div class="d-flex align-items-center p-3" style="background: rgba(248, 249, 250, 0.6); border-radius: 10px;">
                                        <div class="rounded-circle d-flex align-items-center justify-content-center me-3"
                                             style="width: 40px; height: 40px; background: <?php echo $transaction['transaction_type'] === 'credit' ? 'rgba(40, 167, 69, 0.1)' : 'rgba(220, 53, 69, 0.1)'; ?>;">
                                            <i class="fas fa-<?php echo $transaction['transaction_type'] === 'credit' ? 'arrow-down text-success' : 'arrow-up text-danger'; ?>"></i>
                                        </div>
                                        <div class="flex-grow-1">
                                            <h6 class="mb-1" style="font-size: 0.9rem;">
                                                <?php
                                                if ($transaction['type'] === 'transfer') {
                                                    echo htmlspecialchars($transaction['other_party'] ?? 'Transfer');
                                                } else {
                                                    echo htmlspecialchars(ucfirst($transaction['other_party']) ?? 'Transaction');
                                                }
                                                ?>
                                            </h6>
                                            <small class="text-muted">
                                                <?php echo date('M j, g:i A', strtotime($transaction['created_at'])); ?>
                                            </small>
                                        </div>
                                        <div class="text-end">
                                            <div class="fw-semibold <?php echo $transaction['transaction_type'] === 'credit' ? 'text-success' : 'text-danger'; ?>">
                                                <?php echo $transaction['transaction_type'] === 'credit' ? '+' : '-'; ?>$<?php echo number_format($transaction['amount'], 2); ?>
                                            </div>
                                            <small class="text-muted"><?php echo ucfirst($transaction['status']); ?></small>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>

                        <!-- Transaction Summary -->
                        <div class="row mt-4">
                            <div class="col-md-4">
                                <div class="text-center p-3" style="background: rgba(40, 167, 69, 0.05); border-radius: 10px;">
                                    <h6 class="text-success mb-1">$<?php echo number_format($total_credits, 2); ?></h6>
                                    <small class="text-muted">Total Credits</small>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center p-3" style="background: rgba(220, 53, 69, 0.05); border-radius: 10px;">
                                    <h6 class="text-danger mb-1">$<?php echo number_format($total_debits, 2); ?></h6>
                                    <small class="text-muted">Total Debits</small>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center p-3" style="background: rgba(13, 110, 253, 0.05); border-radius: 10px;">
                                    <h6 class="text-primary mb-1"><?php echo $transaction_count + $transfer_count; ?></h6>
                                    <small class="text-muted">This Month</small>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions Section -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm" style="border-radius: 12px;">
                <div class="card-body p-4">
                    <h6 class="card-title mb-3">Quick Actions</h6>
                    <div class="row g-3">
                        <div class="col-md-3">
                            <a href="<?php echo $base_url; ?>/dashboard/transfers/" class="btn btn-outline-primary w-100 py-3" style="border-radius: 10px;">
                                <i class="fas fa-paper-plane mb-2 d-block" style="font-size: 1.5rem;"></i>
                                <small>Send Money</small>
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="<?php echo $base_url; ?>/dashboard/payments/" class="btn btn-outline-success w-100 py-3" style="border-radius: 10px;">
                                <i class="fas fa-file-invoice-dollar mb-2 d-block" style="font-size: 1.5rem;"></i>
                                <small>Pay Bills</small>
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="<?php echo $base_url; ?>/dashboard/cards/" class="btn btn-outline-info w-100 py-3" style="border-radius: 10px;">
                                <i class="fas fa-credit-card mb-2 d-block" style="font-size: 1.5rem;"></i>
                                <small>My Cards</small>
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="<?php echo $base_url; ?>/dashboard/statements/" class="btn btn-outline-warning w-100 py-3" style="border-radius: 10px;">
                                <i class="fas fa-file-alt mb-2 d-block" style="font-size: 1.5rem;"></i>
                                <small>Statements</small>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer template
require_once '../templates/user/footer.php';
?>
