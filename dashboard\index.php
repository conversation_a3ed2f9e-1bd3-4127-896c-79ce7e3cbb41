<?php
// Set page variables
$page_title = 'Dashboard';
$additional_css = ['dashboard.css'];
$additional_js = ['dashboard.js'];

// Include header template
require_once '../templates/user/header.php';

// Include database connection
require_once '../config/config.php';
requireLogin();

// Get comprehensive user data and banking information
try {
    $db = getDB();
    $user_id = $_SESSION['user_id'];

    // Get complete user account information
    $user_sql = "SELECT id, account_number, username, email, first_name, last_name, phone, address,
                        date_of_birth, occupation, marital_status, gender, currency, account_type,
                        balance, status, kyc_status, created_at, last_login
                 FROM accounts WHERE id = ?";
    $user_result = $db->query($user_sql, [$user_id]);
    $user = $user_result->fetch_assoc();

    // Update session with fresh balance
    $_SESSION['balance'] = $user['balance'];
    $current_balance = $user['balance'];

    // Get user's recent transactions
    $sql = "SELECT t.*,
                   CASE
                       WHEN t.sender_id = ? THEN 'sent'
                       ELSE 'received'
                   END as direction,
                   CASE
                       WHEN t.sender_id = ? THEN t.recipient_name
                       ELSE (SELECT CONCAT(first_name, ' ', last_name) FROM accounts WHERE id = t.sender_id)
                   END as other_party
            FROM transfers t
            WHERE (t.sender_id = ? OR t.recipient_id = ?)
            AND t.status = 'completed'
            ORDER BY t.created_at DESC
            LIMIT 5";

    $recent_transactions = $db->query($sql, [$user_id, $user_id, $user_id, $user_id]);

    // Calculate monthly statistics
    $current_month = date('Y-m');
    $stats_sql = "SELECT 
        SUM(CASE WHEN sender_id != ? AND status = 'completed' THEN amount ELSE 0 END) as money_in,
        SUM(CASE WHEN sender_id = ? AND status = 'completed' THEN amount ELSE 0 END) as money_out,
        COUNT(CASE WHEN status = 'completed' AND DATE_FORMAT(created_at, '%Y-%m') = ? THEN 1 END) as total_transactions
        FROM transfers 
        WHERE (sender_id = ? OR recipient_id = ?) 
        AND DATE_FORMAT(created_at, '%Y-%m') = ?";
    
    $stats_result = $db->query($stats_sql, [$user_id, $user_id, $current_month, $user_id, $user_id, $current_month]);
    $stats = $stats_result->fetch_assoc();

    $money_in = $stats['money_in'] ?? 0;
    $money_out = $stats['money_out'] ?? 0;
    $total_transactions = $stats['total_transactions'] ?? 0;
    $net_flow = $money_in - $money_out;

    // Get virtual cards count (if table exists)
    $card_count = 0;
    try {
        $card_result = $db->query("SELECT COUNT(*) as count FROM virtual_cards WHERE user_id = ?", [$user_id]);
        $card_count = $card_result->fetch_assoc()['count'] ?? 0;
    } catch (Exception $e) {
        // Table might not exist, default to 0
        $card_count = 0;
    }

} catch (Exception $e) {
    error_log("Dashboard error: " . $e->getMessage());
    $user = [];
    $current_balance = 0;
    $money_in = $money_out = $total_transactions = $card_count = 0;
    $recent_transactions = [];
}
?>

<!-- Include Sidebar -->
<?php require_once '../templates/user/sidebar.php'; ?>

<!-- Main Content -->
<div class="main-content">
    <!-- Modern Header Design - Admin Style -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="page-title mb-1" style="color: #1a1a1a; font-weight: 600;">Dashboard</h2>
                    <div class="text-muted" style="font-size: 0.9rem;">Welcome back, <?php echo htmlspecialchars($user['first_name'] ?? 'User'); ?>! Here's your banking overview.</div>
                </div>
                <div class="d-flex align-items-center gap-3">
                    <span class="badge" style="background: #d4edda; color: #155724; border: 1px solid #c3e6cb; padding: 8px 16px; border-radius: 20px; font-weight: 500;">
                        Account Active
                    </span>
                    <div class="admin-avatar" style="background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);">
                        <?php echo strtoupper(substr($user['first_name'] ?? 'U', 0, 1)); ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards - Admin Style -->
    <div class="row mb-4">
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="card" style="border: 1px solid #e9ecef; border-radius: 12px; box-shadow: 0 2px 4px rgba(0,0,0,0.04);">
                <div class="card-body" style="padding: 20px;">
                    <div class="d-flex align-items-center">
                        <div class="admin-avatar" style="background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%); margin-right: 16px;">
                            <i class="fas fa-arrow-down" style="font-size: 1rem;"></i>
                        </div>
                        <div>
                            <div style="font-size: 1.5rem; font-weight: 600; color: #1a1a1a; margin-bottom: 4px;">
                                USD <?php echo number_format($money_in, 2); ?>
                            </div>
                            <div style="color: #6b7280; font-size: 0.875rem;">Money In This Month</div>
                            <div style="margin-top: 8px;">
                                <span style="background: #d4edda; color: #155724; padding: 2px 8px; border-radius: 12px; font-size: 0.75rem; font-weight: 500;">
                                    +12.5%
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3 col-sm-6 mb-3">
            <div class="card" style="border: 1px solid #e9ecef; border-radius: 12px; box-shadow: 0 2px 4px rgba(0,0,0,0.04);">
                <div class="card-body" style="padding: 20px;">
                    <div class="d-flex align-items-center">
                        <div class="admin-avatar" style="background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); margin-right: 16px;">
                            <i class="fas fa-arrow-up" style="font-size: 1rem;"></i>
                        </div>
                        <div>
                            <div style="font-size: 1.5rem; font-weight: 600; color: #1a1a1a; margin-bottom: 4px;">
                                USD <?php echo number_format($money_out, 2); ?>
                            </div>
                            <div style="color: #6b7280; font-size: 0.875rem;">Money Out This Month</div>
                            <div style="margin-top: 8px;">
                                <span style="background: #f8d7da; color: #721c24; padding: 2px 8px; border-radius: 12px; font-size: 0.75rem; font-weight: 500;">
                                    -8.3%
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3 col-sm-6 mb-3">
            <div class="card" style="border: 1px solid #e9ecef; border-radius: 12px; box-shadow: 0 2px 4px rgba(0,0,0,0.04);">
                <div class="card-body" style="padding: 20px;">
                    <div class="d-flex align-items-center">
                        <div class="admin-avatar" style="background: linear-gradient(135deg, #007bff 0%, #0056b3 100%); margin-right: 16px;">
                            <i class="fas fa-wallet" style="font-size: 1rem;"></i>
                        </div>
                        <div>
                            <div style="font-size: 1.5rem; font-weight: 600; color: #1a1a1a; margin-bottom: 4px;">
                                USD <?php echo number_format($current_balance, 2); ?>
                            </div>
                            <div style="color: #6b7280; font-size: 0.875rem;">Current Balance</div>
                            <div style="margin-top: 8px;">
                                <span style="background: #cce7ff; color: #004085; padding: 2px 8px; border-radius: 12px; font-size: 0.75rem; font-weight: 500;">
                                    <?php echo htmlspecialchars($user['account_type'] ?? 'Savings'); ?>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3 col-sm-6 mb-3">
            <div class="card" style="border: 1px solid #e9ecef; border-radius: 12px; box-shadow: 0 2px 4px rgba(0,0,0,0.04);">
                <div class="card-body" style="padding: 20px;">
                    <div class="d-flex align-items-center">
                        <div class="admin-avatar" style="background: linear-gradient(135deg, #6f42c1 0%, #5a2d91 100%); margin-right: 16px;">
                            <i class="fas fa-chart-line" style="font-size: 1rem;"></i>
                        </div>
                        <div>
                            <div style="font-size: 1.5rem; font-weight: 600; color: #1a1a1a; margin-bottom: 4px;">
                                <?php echo $total_transactions; ?>
                            </div>
                            <div style="color: #6b7280; font-size: 0.875rem;">Total Activities</div>
                            <div style="margin-top: 8px;">
                                <span style="background: #e2d9f3; color: #5a2d91; padding: 2px 8px; border-radius: 12px; font-size: 0.75rem; font-weight: 500;">
                                    This month
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Account Overview Card - Admin Style -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card" style="border: 1px solid #e9ecef; border-radius: 12px; box-shadow: 0 2px 4px rgba(0,0,0,0.04);">
                <div class="card-header" style="background: #f8f9fa; border-bottom: 1px solid #e9ecef; border-radius: 12px 12px 0 0; padding: 20px 24px;">
                    <div class="d-flex justify-content-between align-items-center">
                        <h3 class="card-title mb-0" style="color: #1a1a1a; font-weight: 600; font-size: 1.1rem;">Account Overview</h3>
                        <div class="d-flex gap-2">
                            <span class="badge" style="background: #d4edda; color: #155724; border: 1px solid #c3e6cb; padding: 6px 12px; border-radius: 16px; font-size: 0.75rem; font-weight: 500;">
                                Active
                            </span>
                            <span class="badge" style="background: #cce7ff; color: #004085; border: 1px solid #99d3ff; padding: 6px 12px; border-radius: 16px; font-size: 0.75rem; font-weight: 500;">
                                KYC Verified
                            </span>
                        </div>
                    </div>
                </div>
                <div class="card-body" style="padding: 24px;">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="d-flex align-items-center mb-3">
                                <div class="admin-avatar admin-avatar-lg" style="background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%); margin-right: 16px;">
                                    <?php echo strtoupper(substr($user['first_name'] ?? 'U', 0, 1) . substr($user['last_name'] ?? 'S', 0, 1)); ?>
                                </div>
                                <div>
                                    <h4 style="color: #1a1a1a; font-weight: 600; margin-bottom: 4px;">
                                        <?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?>
                                    </h4>
                                    <p style="color: #6b7280; font-size: 0.875rem; margin-bottom: 0;">
                                        Account #<?php echo htmlspecialchars($user['account_number'] ?? ''); ?>
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div style="text-align: center; padding: 16px; background: rgba(79, 70, 229, 0.05); border-radius: 12px;">
                                <div style="color: #6b7280; font-size: 0.875rem; margin-bottom: 8px;">Available Balance</div>
                                <h2 style="color: #1a1a1a; font-weight: 700; font-size: 2rem; margin-bottom: 8px;">
                                    USD <?php echo number_format($current_balance, 2); ?>
                                </h2>
                                <div style="color: #6b7280; font-size: 0.875rem;">
                                    <?php echo htmlspecialchars($user['account_type'] ?? 'Savings Account'); ?> • <?php echo htmlspecialchars($user['currency'] ?? 'USD'); ?>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div style="display: flex; flex-direction: column; gap: 12px;">
                                <a href="<?php echo $base_url; ?>/dashboard/transfers/" class="btn" style="background: #007bff; border-color: #007bff; color: white; border-radius: 8px; padding: 10px 16px; font-weight: 500; text-decoration: none; display: flex; align-items: center; gap: 8px;">
                                    <i class="fas fa-exchange-alt"></i>
                                    Transfer Money
                                </a>
                                <a href="<?php echo $base_url; ?>/dashboard/cards/" class="btn" style="background: #6c757d; border-color: #6c757d; color: white; border-radius: 8px; padding: 10px 16px; font-weight: 500; text-decoration: none; display: flex; align-items: center; gap: 8px;">
                                    <i class="fas fa-credit-card"></i>
                                    Manage Cards
                                </a>
                                <a href="<?php echo $base_url; ?>/dashboard/transactions/" class="btn" style="background: #28a745; border-color: #28a745; color: white; border-radius: 8px; padding: 10px 16px; font-weight: 500; text-decoration: none; display: flex; align-items: center; gap: 8px;">
                                    <i class="fas fa-file-alt"></i>
                                    View Statements
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Transactions - Admin Style -->
    <div class="row">
        <div class="col-12">
            <div class="card" style="border: 1px solid #e9ecef; border-radius: 12px; box-shadow: 0 2px 4px rgba(0,0,0,0.04);">
                <div class="card-header" style="background: #f8f9fa; border-bottom: 1px solid #e9ecef; border-radius: 12px 12px 0 0; padding: 20px 24px;">
                    <div class="d-flex justify-content-between align-items-center">
                        <h3 class="card-title mb-0" style="color: #1a1a1a; font-weight: 600; font-size: 1.1rem;">Recent Transactions</h3>
                        <a href="<?php echo $base_url; ?>/dashboard/transactions/" class="btn" style="background: #007bff; border-color: #007bff; color: white; border-radius: 8px; padding: 6px 16px; font-weight: 500; font-size: 0.875rem; text-decoration: none;">
                            <i class="fas fa-list me-1"></i>
                            View All
                        </a>
                    </div>
                </div>
                <div class="card-body p-0">
                    <?php if ($recent_transactions && $recent_transactions->num_rows > 0): ?>
                    <div class="table-responsive">
                        <table class="admin-table" style="border: none;">
                            <thead style="background: #f8f9fa; border-bottom: 1px solid #e9ecef;">
                                <tr>
                                    <th style="padding: 16px 20px; border: none; font-weight: 600; color: #495057; font-size: 0.875rem;">Date</th>
                                    <th style="padding: 16px 20px; border: none; font-weight: 600; color: #495057; font-size: 0.875rem;">Transaction Details</th>
                                    <th style="padding: 16px 20px; border: none; font-weight: 600; color: #495057; font-size: 0.875rem;">Amount</th>
                                    <th style="padding: 16px 20px; border: none; font-weight: 600; color: #495057; font-size: 0.875rem;">Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php while ($transaction = $recent_transactions->fetch_assoc()): ?>
                                <tr style="border-bottom: 1px solid #f1f3f4;">
                                    <td style="padding: 16px 20px; border: none;">
                                        <div style="font-size: 0.875rem; color: #495057; font-weight: 500;"><?php echo date('M j, Y', strtotime($transaction['created_at'])); ?></div>
                                        <small class="text-muted" style="font-size: 0.75rem;"><?php echo date('g:i A', strtotime($transaction['created_at'])); ?></small>
                                    </td>
                                    <td style="padding: 16px 20px; border: none;">
                                        <div class="d-flex align-items-center">
                                            <div class="admin-avatar admin-avatar-sm" style="background: linear-gradient(135deg, <?php echo $transaction['direction'] === 'sent' ? '#dc3545' : '#28a745'; ?> 0%, <?php echo $transaction['direction'] === 'sent' ? '#c82333' : '#1e7e34'; ?> 100%); margin-right: 12px;">
                                                <i class="fas fa-<?php echo $transaction['direction'] === 'sent' ? 'arrow-up' : 'arrow-down'; ?>" style="font-size: 0.75rem;"></i>
                                            </div>
                                            <div>
                                                <div style="font-weight: 500; color: #1a1a1a; font-size: 0.875rem;">
                                                    <?php if ($transaction['direction'] === 'sent'): ?>
                                                        Transfer to <?php echo htmlspecialchars($transaction['other_party']); ?>
                                                    <?php else: ?>
                                                        Transfer from <?php echo htmlspecialchars($transaction['other_party']); ?>
                                                    <?php endif; ?>
                                                </div>
                                                <div style="color: #6b7280; font-size: 0.75rem;">
                                                    <?php echo $transaction['direction'] === 'sent' ? 'Outgoing Transfer' : 'Incoming Transfer'; ?>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                    <td style="padding: 16px 20px; border: none;">
                                        <div style="font-weight: 600; font-size: 0.875rem; color: <?php echo $transaction['direction'] === 'sent' ? '#dc3545' : '#28a745'; ?>;">
                                            <?php echo $transaction['direction'] === 'sent' ? '-' : '+'; ?>USD <?php echo number_format($transaction['amount'], 2); ?>
                                        </div>
                                    </td>
                                    <td style="padding: 16px 20px; border: none;">
                                        <span class="badge" style="background: #d4edda; color: #155724; border: 1px solid #c3e6cb; padding: 6px 12px; border-radius: 16px; font-size: 0.75rem; font-weight: 500;">
                                            <?php echo ucfirst($transaction['status']); ?>
                                        </span>
                                    </td>
                                </tr>
                                <?php endwhile; ?>
                            </tbody>
                        </table>
                    </div>
                    <?php else: ?>
                    <div class="empty" style="padding: 60px 20px; text-align: center;">
                        <div class="empty-img mb-4">
                            <i class="fas fa-chart-line" style="font-size: 4rem; color: #007bff; opacity: 0.8;"></i>
                        </div>
                        <h4 class="empty-title" style="color: #1a1a1a; font-weight: 600; margin-bottom: 8px;">No Transactions Yet</h4>
                        <p class="empty-subtitle text-muted" style="font-size: 0.9rem; margin-bottom: 24px;">
                            Your transaction history will appear here once you start banking with us.
                        </p>
                        <div class="empty-action">
                            <a href="<?php echo $base_url; ?>/dashboard/transfers/" class="btn" style="background: #007bff; border-color: #007bff; color: white; border-radius: 8px; padding: 10px 20px; font-weight: 500; text-decoration: none; margin-right: 12px;">
                                <i class="fas fa-paper-plane me-2"></i>
                                Send Money
                            </a>
                            <a href="<?php echo $base_url; ?>/dashboard/cards/" class="btn" style="background: #6c757d; border-color: #6c757d; color: white; border-radius: 8px; padding: 10px 20px; font-weight: 500; text-decoration: none;">
                                <i class="fas fa-credit-card me-2"></i>
                                Get a Card
                            </a>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer template
require_once '../templates/user/footer.php';
?>
