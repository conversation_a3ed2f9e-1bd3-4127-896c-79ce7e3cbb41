<?php
require_once '../config/config.php';
requireAdmin();

$page_title = 'Security Settings Management';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $db = getDB();
        
        $otp_enabled = isset($_POST['otp_enabled']) ? 1 : 0;
        $google_2fa_enabled = isset($_POST['google_2fa_enabled']) ? 1 : 0;
        $login_attempts_limit = intval($_POST['login_attempts_limit']);
        $lockout_duration = intval($_POST['lockout_duration']);
        $otp_expiry_minutes = intval($_POST['otp_expiry_minutes']);
        $require_2fa_for_admin = isset($_POST['require_2fa_for_admin']) ? 1 : 0;
        $require_2fa_for_users = isset($_POST['require_2fa_for_users']) ? 1 : 0;
        $allow_remember_device = isset($_POST['allow_remember_device']) ? 1 : 0;
        
        // Validate inputs
        if ($login_attempts_limit < 1 || $login_attempts_limit > 20) {
            throw new Exception("Login attempts limit must be between 1 and 20.");
        }
        
        if ($lockout_duration < 1 || $lockout_duration > 1440) {
            throw new Exception("Lockout duration must be between 1 and 1440 minutes (24 hours).");
        }
        
        if ($otp_expiry_minutes < 1 || $otp_expiry_minutes > 60) {
            throw new Exception("OTP expiry must be between 1 and 60 minutes.");
        }
        
        // Deactivate current active settings
        $deactivate_current = "UPDATE security_settings SET is_active = 0 WHERE is_active = 1";
        $db->query($deactivate_current);
        
        // Insert new settings
        $insert_settings = "INSERT INTO security_settings (
            otp_enabled, google_2fa_enabled, login_attempts_limit, lockout_duration,
            otp_expiry_minutes, require_2fa_for_admin, require_2fa_for_users,
            allow_remember_device, created_by, updated_by
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        $settings_id = $db->insert($insert_settings, [
            $otp_enabled, $google_2fa_enabled, $login_attempts_limit, $lockout_duration,
            $otp_expiry_minutes, $require_2fa_for_admin, $require_2fa_for_users,
            $allow_remember_device, $_SESSION['user_id'], $_SESSION['user_id']
        ]);
        
        if ($settings_id) {
            $success = "✅ Security settings updated successfully!";
        } else {
            throw new Exception("Failed to update security settings.");
        }
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// Get current active security settings
try {
    $db = getDB();
    
    $settings_query = "SELECT ss.*, 
                      created_admin.first_name as created_by_first_name, created_admin.last_name as created_by_last_name,
                      updated_admin.first_name as updated_by_first_name, updated_admin.last_name as updated_by_last_name
                      FROM security_settings ss
                      LEFT JOIN accounts created_admin ON ss.created_by = created_admin.id
                      LEFT JOIN accounts updated_admin ON ss.updated_by = updated_admin.id
                      WHERE ss.is_active = 1 
                      ORDER BY ss.created_at DESC 
                      LIMIT 1";
    $settings_result = $db->query($settings_query);
    $current_settings = $settings_result ? $settings_result->fetch_assoc() : null;
    
    // Get security statistics
    $stats_query = "SELECT 
                    COUNT(CASE WHEN last_login IS NOT NULL THEN 1 END) as users_with_login,
                    COUNT(CASE WHEN account_status = 'Active' THEN 1 END) as active_users,
                    COUNT(CASE WHEN account_status IN ('Disabled', 'Suspend') THEN 1 END) as locked_users
                    FROM accounts WHERE is_admin = 0";
    $stats_result = $db->query($stats_query);
    $security_stats = $stats_result ? $stats_result->fetch_assoc() : ['users_with_login' => 0, 'active_users' => 0, 'locked_users' => 0];
    
} catch (Exception $e) {
    $error = "Failed to load security settings: " . $e->getMessage();
    $current_settings = null;
    $security_stats = ['users_with_login' => 0, 'active_users' => 0, 'locked_users' => 0];
}

include 'includes/admin-header.php';
?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" style="margin-bottom: 1rem;">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="index.php">Admin</a></li>
        <li class="breadcrumb-item active" aria-current="page">Security Settings</li>
    </ol>
</nav>

<!-- Success Message -->
<?php if (isset($success)): ?>
<div class="alert alert-success alert-dismissible" role="alert">
    <div class="d-flex">
        <div><i class="fas fa-check-circle me-2"></i></div>
        <div>
            <h4 class="alert-title">Success!</h4>
            <div class="text-muted"><?php echo $success; ?></div>
        </div>
    </div>
    <a class="btn-close" data-bs-dismiss="alert" aria-label="close"></a>
</div>
<?php endif; ?>

<!-- Error Message -->
<?php if (isset($error)): ?>
<div class="alert alert-danger" role="alert">
    <div class="d-flex">
        <div><i class="fas fa-exclamation-circle me-2"></i></div>
        <div>
            <h4 class="alert-title">Error!</h4>
            <div class="text-muted"><?php echo htmlspecialchars($error); ?></div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Security Statistics -->
<div class="row row-cards mb-4">
    <div class="col-sm-6 col-lg-3">
        <div class="card card-sm">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <span class="bg-success text-white avatar">
                            <i class="fas fa-users"></i>
                        </span>
                    </div>
                    <div class="col">
                        <div class="font-weight-medium"><?php echo number_format($security_stats['active_users']); ?></div>
                        <div class="text-muted">Active Users</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-sm-6 col-lg-3">
        <div class="card card-sm">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <span class="bg-info text-white avatar">
                            <i class="fas fa-sign-in-alt"></i>
                        </span>
                    </div>
                    <div class="col">
                        <div class="font-weight-medium"><?php echo number_format($security_stats['users_with_login']); ?></div>
                        <div class="text-muted">Users with Login</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-sm-6 col-lg-3">
        <div class="card card-sm">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <span class="bg-warning text-white avatar">
                            <i class="fas fa-lock"></i>
                        </span>
                    </div>
                    <div class="col">
                        <div class="font-weight-medium"><?php echo number_format($security_stats['locked_users']); ?></div>
                        <div class="text-muted">Locked Users</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-sm-6 col-lg-3">
        <div class="card card-sm">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <span class="bg-<?php echo ($current_settings['otp_enabled'] ?? 0) ? 'success' : 'danger'; ?> text-white avatar">
                            <i class="fas fa-shield-alt"></i>
                        </span>
                    </div>
                    <div class="col">
                        <div class="font-weight-medium"><?php echo ($current_settings['otp_enabled'] ?? 0) ? 'Enabled' : 'Disabled'; ?></div>
                        <div class="text-muted">2FA Status</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Current Settings Display - Full Width -->
    <div class="col-12">
        <?php if ($current_settings): ?>
        <div class="card mb-4">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-shield-alt me-2"></i>
                    Current Security Configuration
                </h3>
                <div class="card-actions">
                    <button type="button" class="btn btn-primary btn-sm" onclick="editSecuritySettings()">
                        <i class="fas fa-edit me-2"></i>
                        Edit Settings
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <dl class="row">
                            <dt class="col-sm-6">OTP Authentication:</dt>
                            <dd class="col-sm-6">
                                <span class="badge bg-<?php echo $current_settings['otp_enabled'] ? 'success' : 'danger'; ?> badge-sm">
                                    <?php echo $current_settings['otp_enabled'] ? 'Enabled' : 'Disabled'; ?>
                                </span>
                            </dd>

                            <dt class="col-sm-6">Google 2FA:</dt>
                            <dd class="col-sm-6">
                                <span class="badge bg-<?php echo $current_settings['google_2fa_enabled'] ? 'success' : 'secondary'; ?> badge-sm">
                                    <?php echo $current_settings['google_2fa_enabled'] ? 'Enabled' : 'Disabled'; ?>
                                </span>
                            </dd>

                            <dt class="col-sm-6">Login Attempts:</dt>
                            <dd class="col-sm-6"><code><?php echo $current_settings['login_attempts_limit']; ?> attempts</code></dd>

                            <dt class="col-sm-6">Lockout Duration:</dt>
                            <dd class="col-sm-6"><code><?php echo $current_settings['lockout_duration']; ?> minutes</code></dd>
                        </dl>
                    </div>
                    <div class="col-md-6">
                        <dl class="row">
                            <dt class="col-sm-6">OTP Expiry:</dt>
                            <dd class="col-sm-6"><code><?php echo $current_settings['otp_expiry_minutes']; ?> minutes</code></dd>

                            <dt class="col-sm-6">Admin 2FA:</dt>
                            <dd class="col-sm-6">
                                <span class="badge bg-<?php echo $current_settings['require_2fa_for_admin'] ? 'success' : 'warning'; ?> badge-sm">
                                    <?php echo $current_settings['require_2fa_for_admin'] ? 'Required' : 'Optional'; ?>
                                </span>
                            </dd>

                            <dt class="col-sm-6">User 2FA:</dt>
                            <dd class="col-sm-6">
                                <span class="badge bg-<?php echo $current_settings['require_2fa_for_users'] ? 'success' : 'warning'; ?> badge-sm">
                                    <?php echo $current_settings['require_2fa_for_users'] ? 'Required' : 'Optional'; ?>
                                </span>
                            </dd>

                            <dt class="col-sm-6">Remember Device:</dt>
                            <dd class="col-sm-6">
                                <span class="badge bg-<?php echo $current_settings['allow_remember_device'] ? 'info' : 'secondary'; ?> badge-sm">
                                    <?php echo $current_settings['allow_remember_device'] ? 'Allowed' : 'Disabled'; ?>
                                </span>
                            </dd>
                        </dl>
                    </div>
                </div>

                <hr>

                <div class="row">
                    <div class="col-md-6">
                        <small class="text-muted">
                            <strong>Last Updated:</strong> <?php echo date('M j, Y g:i A', strtotime($current_settings['updated_at'])); ?>
                        </small>
                    </div>
                    <div class="col-md-6">
                        <small class="text-muted">
                            <strong>Updated By:</strong> <?php echo htmlspecialchars($current_settings['updated_by_first_name'] . ' ' . $current_settings['updated_by_last_name']); ?>
                        </small>
                    </div>
                </div>
            </div>
        </div>
        <?php else: ?>
        <div class="card mb-4">
            <div class="card-body">
                <div class="empty">
                    <div class="empty-img">
                        <i class="fas fa-shield-alt" style="font-size: 3rem; color: #6c757d;"></i>
                    </div>
                    <p class="empty-title">No Security Settings</p>
                    <p class="empty-subtitle text-muted">
                        No security configuration has been set up yet.
                    </p>
                    <div class="empty-action">
                        <button type="button" class="btn btn-primary" onclick="editSecuritySettings()">
                            <i class="fas fa-plus me-2"></i>
                            Configure Security Settings
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Edit Form (Hidden by default) -->
        <div class="card" id="editForm" style="display: none;">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-edit me-2"></i>
                    Update Security Settings
                </h3>
            </div>
            <div class="card-body">
                <form method="POST" action="">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h4 class="card-title">Authentication Methods</h4>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label class="form-check">
                                            <input type="checkbox" name="otp_enabled" class="form-check-input"
                                                   <?php echo ($current_settings['otp_enabled'] ?? 1) ? 'checked' : ''; ?>>
                                            <span class="form-check-label">
                                                <strong>Enable OTP Authentication</strong>
                                                <span class="form-check-description">Send OTP codes via email for login verification</span>
                                            </span>
                                        </label>
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-check">
                                            <input type="checkbox" name="google_2fa_enabled" class="form-check-input"
                                                   <?php echo ($current_settings['google_2fa_enabled'] ?? 0) ? 'checked' : ''; ?>>
                                            <span class="form-check-label">
                                                <strong>Enable Google 2FA</strong>
                                                <span class="form-check-description">Use Google Authenticator for 2FA - Configure in <a href="configure-2fa.php">2FA Configuration</a></span>
                                            </span>
                                        </label>
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label">OTP Expiry Time</label>
                                        <div class="input-group">
                                            <input type="number" name="otp_expiry_minutes" class="form-control"
                                                   min="1" max="60" value="<?php echo $current_settings['otp_expiry_minutes'] ?? 10; ?>">
                                            <span class="input-group-text">minutes</span>
                                        </div>
                                        <small class="form-hint">How long OTP codes remain valid</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h4 class="card-title">Login Security</h4>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label class="form-label">Login Attempts Limit</label>
                                        <input type="number" name="login_attempts_limit" class="form-control"
                                               min="1" max="20" value="<?php echo $current_settings['login_attempts_limit'] ?? 5; ?>">
                                        <small class="form-hint">Maximum failed login attempts before lockout</small>
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label">Lockout Duration</label>
                                        <div class="input-group">
                                            <input type="number" name="lockout_duration" class="form-control"
                                                   min="1" max="1440" value="<?php echo $current_settings['lockout_duration'] ?? 30; ?>">
                                            <span class="input-group-text">minutes</span>
                                        </div>
                                        <small class="form-hint">How long accounts remain locked after failed attempts</small>
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-check">
                                            <input type="checkbox" name="allow_remember_device" class="form-check-input"
                                                   <?php echo ($current_settings['allow_remember_device'] ?? 0) ? 'checked' : ''; ?>>
                                            <span class="form-check-label">
                                                <strong>Allow Remember Device</strong>
                                                <span class="form-check-description">Users can skip 2FA on trusted devices</span>
                                            </span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-3">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h4 class="card-title">2FA Requirements</h4>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label class="form-check">
                                            <input type="checkbox" name="require_2fa_for_admin" class="form-check-input"
                                                   <?php echo ($current_settings['require_2fa_for_admin'] ?? 1) ? 'checked' : ''; ?>>
                                            <span class="form-check-label">
                                                <strong>Require 2FA for Admins</strong>
                                                <span class="form-check-description">All admin accounts must use 2FA</span>
                                            </span>
                                        </label>
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-check">
                                            <input type="checkbox" name="require_2fa_for_users" class="form-check-input"
                                                   <?php echo ($current_settings['require_2fa_for_users'] ?? 1) ? 'checked' : ''; ?>>
                                            <span class="form-check-label">
                                                <strong>Require 2FA for Users</strong>
                                                <span class="form-check-description">All user accounts must use 2FA</span>
                                            </span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h4 class="card-title">Quick Actions</h4>
                                </div>
                                <div class="card-body">
                                    <div class="btn-list">
                                        <button type="button" class="btn btn-warning btn-sm" onclick="disableAllSecurity()">
                                            <i class="fas fa-unlock me-2"></i>
                                            Disable All Security
                                        </button>
                                        <button type="button" class="btn btn-success btn-sm" onclick="enableMaxSecurity()">
                                            <i class="fas fa-shield-alt me-2"></i>
                                            Maximum Security
                                        </button>
                                    </div>
                                    <small class="form-hint">Quick presets for common configurations</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-footer mt-3">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            Save Security Settings
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="cancelEdit()">
                            <i class="fas fa-times me-2"></i>
                            Cancel
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Quick Guidelines - Full Width -->
<div class="row">
    <div class="col-12">
        <div class="card mb-4" style="border: 1px solid #e9ecef; border-radius: 12px; box-shadow: 0 2px 4px rgba(0,0,0,0.04);">
            <div class="card-header" style="background: #f8f9fa; border-bottom: 1px solid #e9ecef; border-radius: 12px 12px 0 0; padding: 16px 20px;">
                <h3 class="card-title mb-0" style="color: #1a1a1a; font-weight: 600; font-size: 1rem;">
                    <i class="fas fa-info-circle me-2" style="color: #6b7280;"></i>
                    Security Quick Reference
                </h3>
            </div>
            <div class="card-body" style="padding: 20px;">
                <!-- Current Status -->
                <div class="row mb-3">
                    <div class="col-6">
                        <div class="d-flex align-items-center p-3" style="background: rgba(<?php echo ($current_settings['require_2fa_for_admin'] ?? 1) ? '34, 197, 94' : '245, 158, 11'; ?>, 0.1); border-radius: 8px; border-left: 3px solid <?php echo ($current_settings['require_2fa_for_admin'] ?? 1) ? '#22c55e' : '#f59e0b'; ?>;">
                            <i class="fas fa-<?php echo ($current_settings['require_2fa_for_admin'] ?? 1) ? 'shield-alt' : 'unlock'; ?> me-2" style="color: <?php echo ($current_settings['require_2fa_for_admin'] ?? 1) ? '#22c55e' : '#f59e0b'; ?>; font-size: 1.1rem;"></i>
                            <div>
                                <div style="font-weight: 600; color: #1a1a1a; font-size: 0.9rem;">Admin 2FA</div>
                                <div style="color: #6b7280; font-size: 0.8rem;"><?php echo ($current_settings['require_2fa_for_admin'] ?? 1) ? 'Required' : 'Optional'; ?></div>
                            </div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="d-flex align-items-center p-3" style="background: rgba(<?php echo ($current_settings['require_2fa_for_users'] ?? 1) ? '34, 197, 94' : '59, 130, 246'; ?>, 0.1); border-radius: 8px; border-left: 3px solid <?php echo ($current_settings['require_2fa_for_users'] ?? 1) ? '#22c55e' : '#3b82f6'; ?>;">
                            <i class="fas fa-<?php echo ($current_settings['require_2fa_for_users'] ?? 1) ? 'shield-alt' : 'users'; ?> me-2" style="color: <?php echo ($current_settings['require_2fa_for_users'] ?? 1) ? '#22c55e' : '#3b82f6'; ?>; font-size: 1.1rem;"></i>
                            <div>
                                <div style="font-weight: 600; color: #1a1a1a; font-size: 0.9rem;">User 2FA</div>
                                <div style="color: #6b7280; font-size: 0.8rem;"><?php echo ($current_settings['require_2fa_for_users'] ?? 1) ? 'Required' : 'Optional'; ?></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Methods & Levels -->
                <div class="row mb-3">
                    <div class="col-md-6">
                        <div style="background: #f8f9fa; border-radius: 8px; padding: 16px;">
                            <h6 style="color: #1a1a1a; font-weight: 600; margin-bottom: 12px; font-size: 0.9rem;">
                                <i class="fas fa-key me-1" style="color: #6b7280;"></i>Available Methods
                            </h6>
                            <div class="d-flex flex-column gap-2">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-envelope me-2" style="color: #3b82f6; font-size: 0.9rem;"></i>
                                    <span style="font-size: 0.85rem; color: #1a1a1a;">Email OTP</span>
                                </div>
                                <div class="d-flex align-items-center">
                                    <i class="fab fa-google me-2" style="color: #22c55e; font-size: 0.9rem;"></i>
                                    <span style="font-size: 0.85rem; color: #1a1a1a;">Google Authenticator</span>
                                </div>
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-user-shield me-2" style="color: #f59e0b; font-size: 0.9rem;"></i>
                                    <span style="font-size: 0.85rem; color: #1a1a1a;">Admin Manual OTP</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div style="background: #f8f9fa; border-radius: 8px; padding: 16px;">
                            <h6 style="color: #1a1a1a; font-weight: 600; margin-bottom: 12px; font-size: 0.9rem;">
                                <i class="fas fa-layer-group me-1" style="color: #6b7280;"></i>Security Levels
                            </h6>
                            <div class="d-flex flex-column gap-2">
                                <div class="d-flex align-items-center justify-content-between">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-unlock me-2" style="color: #ef4444; font-size: 0.9rem;"></i>
                                        <span style="font-size: 0.85rem; color: #1a1a1a;">Basic</span>
                                    </div>
                                    <span style="font-size: 0.75rem; color: #6b7280;">Password Only</span>
                                </div>
                                <div class="d-flex align-items-center justify-content-between">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-envelope me-2" style="color: #f59e0b; font-size: 0.9rem;"></i>
                                        <span style="font-size: 0.85rem; color: #1a1a1a;">Enhanced</span>
                                    </div>
                                    <span style="font-size: 0.75rem; color: #6b7280;">+ Email OTP</span>
                                </div>
                                <div class="d-flex align-items-center justify-content-between">
                                    <div class="d-flex align-items-center">
                                        <i class="fab fa-google me-2" style="color: #22c55e; font-size: 0.9rem;"></i>
                                        <span style="font-size: 0.85rem; color: #1a1a1a;">Maximum</span>
                                    </div>
                                    <span style="font-size: 0.75rem; color: #6b7280;">+ Google 2FA</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Best Practices -->
                <div style="background: linear-gradient(135deg, rgba(34, 197, 94, 0.05) 0%, rgba(59, 130, 246, 0.05) 100%); border-radius: 8px; padding: 16px; border: 1px solid rgba(34, 197, 94, 0.1);">
                    <h6 style="color: #1a1a1a; font-weight: 600; margin-bottom: 12px; font-size: 0.9rem;">
                        <i class="fas fa-lightbulb me-1" style="color: #22c55e;"></i>Quick Tips
                    </h6>
                    <div class="row">
                        <div class="col-md-6">
                            <div style="font-size: 0.8rem; color: #374151; line-height: 1.4;">
                                <strong>Admin:</strong> Always enable 2FA • Use Google Authenticator • Set 3-5 login attempts • 30-60 min lockout
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div style="font-size: 0.8rem; color: #374151; line-height: 1.4;">
                                <strong>Users:</strong> Encourage 2FA adoption • Provide setup guides • Allow device memory • Monitor adoption
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function editSecuritySettings() {
    document.getElementById('editForm').style.display = 'block';
    document.getElementById('editForm').scrollIntoView({ behavior: 'smooth' });
}

function cancelEdit() {
    document.getElementById('editForm').style.display = 'none';
}

function disableAllSecurity() {
    if (confirm('⚠️ This will disable all security features. Are you sure?')) {
        document.querySelector('input[name="otp_enabled"]').checked = false;
        document.querySelector('input[name="google_2fa_enabled"]').checked = false;
        document.querySelector('input[name="require_2fa_for_admin"]').checked = false;
        document.querySelector('input[name="require_2fa_for_users"]').checked = false;
        document.querySelector('input[name="allow_remember_device"]').checked = true;
        document.querySelector('input[name="login_attempts_limit"]').value = 20;
        document.querySelector('input[name="lockout_duration"]').value = 1;
    }
}

function enableMaxSecurity() {
    document.querySelector('input[name="otp_enabled"]').checked = true;
    document.querySelector('input[name="google_2fa_enabled"]').checked = true; // Now available
    document.querySelector('input[name="require_2fa_for_admin"]').checked = true;
    document.querySelector('input[name="require_2fa_for_users"]').checked = true;
    document.querySelector('input[name="allow_remember_device"]').checked = false;
    document.querySelector('input[name="login_attempts_limit"]').value = 3;
    document.querySelector('input[name="lockout_duration"]').value = 60;
    document.querySelector('input[name="otp_expiry_minutes"]').value = 5;
}
</script>

<?php include 'includes/admin-footer.php'; ?>
